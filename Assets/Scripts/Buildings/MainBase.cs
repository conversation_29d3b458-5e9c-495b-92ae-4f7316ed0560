using Common;
using Components;
using Managers;
using ManagersData;
using NaughtyAttributes;
using System.Collections.Generic;
using UnitParts;
using UnityEngine;

namespace Buildings
{
    [RequireComponent(typeof(HealthHandler))]
    [RequireComponent(typeof(ShieldHandler))]
    [RequireComponent(typeof(DamageHandlerComponent))]
    public class MainBase : ResourceHolder
    {
        [ReadOnly] public bool lastEffort;

        [SerializeField] public StateData StateData;

        private readonly HashSet<ResourceKind>
            _acceptedResources = new() { ResourceKind.Stone, ResourceKind.IronIngot };

        [SerializeField] [ReadOnly] [Required] private ShieldHandler _shield;
        private IStateManager _stateManager;

        protected override void UseDefaults()
        {
            base.UseDefaults();
            _shield = GetComponent<ShieldHandler>();
        }

        protected override void Start()
        {
            base.Start();
            _stateManager = ServiceLocator.Get<IStateManager>();

            _shield.SetMaxValue(BuildingData.maxHealth * 2f);

            if (BuildingData.team == TeamEnum.Player)
            {
                // Dont remove the base when it dies
                _damageHandlerComponent.AfterDamageOverFlowCallback -= _ =>
                {
                    // Base is needed for other scripts to find the player base
                    Destroy(gameObject);
                };
                // Instead, set the game state to lose
                _damageHandlerComponent.AfterDamageOverFlowCallback += damage =>
                {
                    _stateManager?.SetGameState(GameState.Lose);
                    gameObject.SetActive(false);
                };
            }

            _health.AfterDamageOverFlow += BeforeDeath;
        }

        protected virtual void Update()
        {
            if (!IsOccupied && TryGetNextResource())
            {
                if (CurrentlyHeldResource.Kind == ResourceKind.Stone)
                {
                    StateData.AddResource(ResourceKind.Stone, 5);
                }
                else if (CurrentlyHeldResource.Kind == ResourceKind.IronIngot)
                {
                    StateData.AddResource(ResourceKind.IronIngot, 5);
                }

                StartCoroutine(ConsumeResourceAfterMove(CurrentlyHeldResource));
                CurrentlyHeldResource = null;
                IsOccupied = false;
            }
        }

        public override bool IsResourceAccepted(IResource resource)
        {
            if (_acceptedResources.Contains(resource.Kind))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        ///     Prevents the building from being destroyed when it reaches 0 health.
        ///     This is used for the last effort mechanic.
        /// </summary>
        /// <param name="damage"></param>
        /// <returns></returns>
        private float BeforeDeath(float damage)
        {
            TriggerLastEffort();
            _health.AfterDamageOverFlow -= BeforeDeath;
            return 0;
        }

        [Button("Trigger Last Effort")]
        private void TriggerLastEffort()
        {
            if (lastEffort)
            {
                return;
            }

            lastEffort = true;
            _shield.ActivateAnimatedShieldForDuration();

            _health.regenRate *= 0.1f;
            _health.ActivateHealing(_health.MaxValue, 10);
        }
    }
}
