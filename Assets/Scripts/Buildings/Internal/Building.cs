using Buildings.Interfaces;
using Common;
using Components;
using EntityData;
using Managers;
using NaughtyAttributes;
using NavMeshCustom;
using System.Collections.Generic;
using UnitParts;
using UnitParts.Interfaces;
using UnityEngine;

namespace Buildings
{
    [RequireComponent(typeof(HealthHandler))]
    [RequireComponent(typeof(DamageHandlerComponent))]
    public class Building : MonoBehaviour, IBuilding, ITeamDamageable
    {
        [Required] [Expandable] [SerializeField]
        public BuildingData BuildingData;

        [SerializeField] [ReadOnly] [Required] protected DamageHandlerComponent _damageHandlerComponent;
        [SerializeField] [ReadOnly] [Required] protected HealthHandler _health;

        public void OnValidate()
        {
            if (BuildingData == null)
            {
                Debug.LogError("BuildingData is not assigned for " + gameObject.name);
                return;
            }

            UseDefaults();
        }

        protected virtual void Awake()
        {
            if (BuildingData.team == TeamEnum.None)
            {
                Debug.LogError("Team is not assigned for " + gameObject.name);
            }

            UseDefaults();
        }

        protected virtual void Start()
        {
            CheckForNavmeshRebuild();
        }

        protected void OnDestroy()
        {
            OnRemove();

            OnDestroyCallback?.Invoke();

            gameObject.SetActive(false);
            CheckForNavmeshRebuild();
        }

        protected void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(Position, Position + transform.up);
            Gizmos.DrawLine(Position, Position + transform.right);
        }

        public Vector2Int Dimensions => BuildingData.dimensions;
        public string Name => BuildingData.buildingName;
        public bool IsRotateable => BuildingData.isRotateable;
        public Dictionary<ResourceKind, int> Costs => BuildingData.Costs;

        [field: BoxGroup("World Entity")]
        [field: SerializeField]
        [field: ReadOnly]
        public Vector2Int Coordinates { get; protected set; }

        public void Build(Vector2Int position, float rotation)
        {
            // Basic initialization
            Position = Utils.ToWorldCoordinates(position);
            Coordinates = position;
            CheckForNavmeshRebuild();

            // Post initialization - register with BuildingManager via dependency injection
            var buildingManager = ServiceLocator.Get<IBuildingManager>();
            if (buildingManager != null)
            {
                buildingManager.InsertBuildingIntoDict(Coordinates, gameObject, Dimensions);
                OnDestroyCallback += buildingManager.CreateAfterBuildingRemovalCallback(gameObject);
            }
            else
            {
                Debug.LogError("BuildingManager not found in ServiceLocator. Building registration failed.");
            }

            // Custom logic for building
            OnBuild(position, rotation);
        }

        public HashSet<Vector2Int> GetSpannedPositions()
        {
            var spannedPositions = new HashSet<Vector2Int>();
            for (var x = 0; x < Dimensions.x; x++)
            {
                for (var y = 0; y < Dimensions.y; y++)
                {
                    spannedPositions.Add(new Vector2Int(Coordinates.x + x, Coordinates.y + y));
                }
            }

            return spannedPositions;
        }

        public bool IsDestroyed => _damageHandlerComponent?.IsDestroyed ?? true;

        public float TakeDamage(float amount) =>
            _damageHandlerComponent != null ? _damageHandlerComponent.TakeDamage(amount) : amount;

        public OnDestroyCallback OnDestroyCallback { get; set; }

        public float TakeHeal(float amount) =>
            _damageHandlerComponent != null ? _damageHandlerComponent.TakeHeal(amount) : amount;

        // Position is managed by the building manager, therefore it is readonly
        // in the editor for debug purposes.
        [field: BoxGroup("World Entity")]
        [field: SerializeField]
        [field: ReadOnly]
        public Vector3 Position { get; protected set; }

        public TeamEnum Team => BuildingData.team;
        public Vector3 Velocity => Vector2.zero;

        public int TargetPriority => BuildingData.targetPriority;

        public void Knockback(Vector3 knocbackDirection, float knockback)
        {
            // Empty method
        }

        /// <summary>
        ///     Template method for building specific logic when the building is removed.
        /// </summary>
        protected virtual void OnRemove() { }

        protected virtual void UseDefaults()
        {
            _damageHandlerComponent ??= GetComponent<DamageHandlerComponent>();

            Position = gameObject.transform.position + new Vector3(Dimensions.x, Dimensions.y, 0) / 2 -
                       new Vector3(0.5f, 0.5f, 0);

            _health ??= GetComponent<HealthHandler>();
            _health?.SetMaxValue(BuildingData.maxHealth);
            _health?.SetRegenRate(BuildingData.healthRegenRate);
        }

        private void CheckForNavmeshRebuild()
        {
            if (!BuildingData.isDisruptingNavMesh)
            {
                return;
            }

            var navMeshManager = ServiceLocator.GetOptional<INavMeshManager>();
            navMeshManager?.RequestRebuildNavmesh();
        }

        /// <summary>
        ///     Template method for building specific logic when the building is built.
        /// </summary>
        protected virtual void OnBuild(Vector2Int position, float rotation) { }

        protected List<Vector2Int> GetAdjacentPositions()
        {
            var adjactedBuildings = new List<Vector2Int>();

            var transformRight = Vector2Int.RoundToInt(transform.right);
            var minRight = Coordinates - transformRight;
            var maxRight = Coordinates + (Dimensions.x * transformRight);

            var transformUp = Vector2Int.RoundToInt(transform.up);
            var minUp = Coordinates - transformUp;
            var maxUp = Coordinates + (Dimensions.y * transformUp);

            for (var i = 0; i < Dimensions.x; ++i)
            {
                adjactedBuildings.Add(minUp + (transformRight * i));
                adjactedBuildings.Add(maxUp + (transformRight * i));
            }

            for (var i = 0; i < Dimensions.y; ++i)
            {
                adjactedBuildings.Add(minRight + (transformUp * i));
                adjactedBuildings.Add(maxRight + (transformUp * i));
            }

            return adjactedBuildings;
        }

        public void MoveBuilding(Vector2Int position) => Coordinates = position;
    }
}
