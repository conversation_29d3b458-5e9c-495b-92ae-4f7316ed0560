using Buildings;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace Common
{
    public abstract class OrderUtils
    {
        public static IBaseOrder DirectAttackOrder(UnitController unit, ITeamDamageable target)
        {
            var moveOrder = MoveTo(unit, target);
            var targetedMoveOrder = new GunTargetDecorator(moveOrder) { target = target };
            var conditionalDecorator = new ConditionalDecorator(targetedMoveOrder)
            {
                condition = () => target.IsDestroyed,
                onCondition = () =>
                {
                    Debug.Log("[ConditionalDecorator] Target destroyed, canceling attack order");
                }
            };
            return conditionalDecorator;
        }

        public static IBaseOrder RangeLimitedDirectAttackOrder(UnitController unit, ITeamDamageable target,
            Vector3 defendPosition, float defendRadius)
        {
            var directAttackOrder = DirectAttackOrder(unit, target);
            var rangeLimitedOrder = new ConditionalDecorator(directAttackOrder)
            {
                condition =
                    () => target.IsDestroyed || Vector3.Distance(target.Position, defendPosition) > defendRadius,
                onCondition = () =>
                {
                    if (target.IsDestroyed)
                    {
                        Debug.Log("[ConditionalDecorator] Target destroyed, canceling attack order");
                        return;
                    }

                    Debug.Log("[ConditionalDecorator] Target out of range, returning to defend position");
                }
            };
            return rangeLimitedOrder;
        }

        public static IBaseOrder AttackMove(UnitController unit, Vector3 targetPosition, float detectionRadius = 6f)
        {
            var attackMoveOrder = AttackMoveOrder.Get();
            attackMoveOrder.Initialize(unit, targetPosition, detectionRadius, unit);
            return attackMoveOrder;
        }

        public static IBaseOrder StayStill(UnitController unit)
        {
            var stayStillOrder = StayStillOrder.Get();
            stayStillOrder.Initialize(unit, unit.movementController);
            return stayStillOrder;
        }

        public static IBaseOrder Defend(UnitController unit, Vector3 defendPosition, float defendRadius = 5f,
            float detectionRadius = 8f)
        {
            var defendOrder = DefendOrder.Get();
            defendOrder.Initialize(unit, defendPosition, defendRadius, detectionRadius, unit);
            return defendOrder;
        }

        /// <summary>
        ///     Move to a random position within a radius of the target position.
        /// </summary>
        public static IBaseOrder RandomWonder(UnitController unit, Vector3 targetPosition, float radius)
        {
            var random2Dposition = Random.insideUnitCircle * radius;
            var randomPosition = targetPosition + new Vector3(random2Dposition.x, random2Dposition.y, 0f);

            var moveOrder = MoveToStaticTarget(unit, randomPosition);

            return moveOrder;
        }

        /// <summary>
        ///     Move to a target that can be either static or dynamic.
        /// </summary>
        public static IBaseOrder MoveTo(UnitController unit, ITeamDamageable target, float stoppingDistance = 0)
        {
            if (target is Building)
            {
                stoppingDistance += MathUtils.CalculateOffset(target);
                return MoveToStaticTarget(unit, target.Position, stoppingDistance);
            }

            return MoveToDynamicTarget(unit, target, stoppingDistance);
        }

        /// <summary>
        ///     Move to a dynamic target that can move. Uses TargetTrackingDecorator to continuously update position.
        /// </summary>
        public static IBaseOrder MoveToDynamicTarget(UnitController unit, ITeamDamageable target,
            float stoppingDistance = 0)
        {
            var totalOffset = MathUtils.CalculateOffset(unit) + MathUtils.CalculateOffset(target);
            var adjustedStoppingDistance = totalOffset + stoppingDistance;

            // Create base move order with initial target position
            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, target.Position, adjustedStoppingDistance, unit.movementController);

            // Wrap with target tracking decorator for dynamic position updates
            var trackingDecorator = new TargetTrackingDecorator(moveOrder)
            {
                target = target,
                stoppingDistance = adjustedStoppingDistance,
                updateInterval = 0.2f // Update every 0.2 seconds
            };

            return trackingDecorator;
        }

        /// <summary>
        ///     Move to a static position. Position will not be updated during movement.
        /// </summary>
        public static IBaseOrder MoveToStaticTarget(UnitController unit, Vector3 targetPosition,
            float stoppingDistance = 0)
        {
            var totalOffset = MathUtils.CalculateOffset(unit);

            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, targetPosition, totalOffset + stoppingDistance, unit.movementController);

            return moveOrder;
        }
    }
}
