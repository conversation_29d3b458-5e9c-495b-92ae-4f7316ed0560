using Buildings;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnityEngine;

namespace Common
{
    public abstract class MathUtils
    {
        private static readonly float s_margin = 0.01f;

        public static float CalculateOffset(ITeamDamageable target)
        {
            Collider2D targetCollider;
            var offset = s_margin;

            if (target is Building targetBuilding)
            {
                targetCollider = targetBuilding.GetComponent<BoxCollider2D>();
                offset += targetCollider.bounds.extents.x;
            }
            else if (target is UnitController targetUnit)
            {
                targetCollider = targetUnit.GetComponent<CircleCollider2D>();
                offset += targetCollider.bounds.extents.x;
            }
            else
            {
                Debug.LogError("Target is not a Building or UnitController.");
            }

            return offset;
        }
    }
}
