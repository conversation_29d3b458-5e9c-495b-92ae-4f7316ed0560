using DG.Tweening;
using System.Collections;
using UnityEngine;

namespace Components
{
    /// <summary>
    ///     Called when the shield is deactivated
    /// </summary>
    public delegate void ShieldDeactivationCallback();

    /// <summary>
    ///     Called when the shield is activated
    /// </summary>
    public delegate void ShieldActivationCallback();

    [RequireComponent(typeof(IDamageableComponent))]
    [DisallowMultipleComponent]
    public class ShieldHandler : BarHandler, IDamageableModule
    {
        [SerializeField] public float shieldDuration;
        public bool Invulnerable { private set; get; }

        public ShieldActivationCallback ShieldActivationCallback { get; set; }
        public ShieldDeactivationCallback ShieldDeactivationCallback { get; set; }

        /// <summary>
        ///     Spawn with zero shield
        /// </summary>
        public void OnEnable()
        {
            CurrentValue = 0;

            var damageableComponent = GetComponent<IDamageableComponent>();
            damageableComponent.RegisterBar(this);
        }

        public void OnDisable()
        {
            var damageableComponent = GetComponent<IDamageableComponent>();
            damageableComponent?.UnregisterBar(this);
        }

        /// <summary>
        ///     Auto assign the bar component to the bar variable
        ///     in the inspector
        /// </summary>
        public void OnValidate()
        {
            if (bar != null)
            {
                return;
            }

            var barsOnObject = GetComponentsInChildren<Bar>();
            foreach (var barOnObject in barsOnObject)
            {
                if (barOnObject.name.Contains("Shield Bar"))
                {
                    bar = barOnObject;
                    break;
                }
            }
        }

        public float TakeDamage(float amount)
        {
            var appliedDamage = Mathf.Min(amount, CurrentValue);
            CurrentValue -= appliedDamage;
            amount -= appliedDamage;

            if (amount > 0)
            {
                AfterDamageOverFlow?.Invoke(amount);
            }

            return amount;
        }

        public float TakeHeal(float amount)
        {
            amount = AfterHealOverFlow?.Invoke(amount) ?? amount;
            return amount;
        }

        public DamageableComponentType ModulePriority => DamageableComponentType.ShieldBar;
        public AfterOverFlowCallback AfterDamageOverFlow { get; set; }

        public AfterOverFlowCallback AfterHealOverFlow { get; set; }
        public bool IsDestroyed => CurrentValue <= 0;

        public override bool WouldUnderFlow(float amount) => !Invulnerable && base.WouldUnderFlow(amount);

        public void ActivateAnimatedShieldForDuration() => StartCoroutine(AnimatedShieldLifetimeRoutine());

        private IEnumerator AnimatedShieldLifetimeRoutine()
        {
            ShieldActivationCallback?.Invoke();
            var delay = shieldDuration / 10;

            Invulnerable = true;
            DOTween.To(() => CurrentValue, x => CurrentValue = x, MaxValue, delay);
            yield return new WaitForSeconds(delay);

            Invulnerable = true;
            yield return new WaitForSeconds(shieldDuration);

            DOTween.To(() => CurrentValue, x => CurrentValue = x, 0, delay);
            yield return new WaitForSeconds(delay);

            ShieldDeactivationCallback?.Invoke();
            CurrentValue = 0; // Deactivate
        }
    }
}
