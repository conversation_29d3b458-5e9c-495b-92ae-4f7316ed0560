using NaughtyAttributes;
using UnityEngine;

namespace Components
{
    /// <summary>
    ///     Handles the health value and regeneration of a health bar.
    ///     Adds a callback to be called when the health bar is destroyed.
    ///     Destroys the game object when the health is zero.
    /// </summary>
    [RequireComponent(typeof(IDamageableComponent))]
    [DisallowMultipleComponent]
    public class HealthHandler : BarHandler, IDamageableModule
    {
        [SerializeField] [ReadOnly] public float regenRate;

        private void Update() => Heal(regenRate * Time.deltaTime);

        /// <summary>
        ///     Spawn with full health
        /// </summary>
        public void OnEnable()
        {
            CurrentValue = MaxValue;

            var damageableComponent = GetComponent<IDamageableComponent>();
            damageableComponent.RegisterBar(this);
        }

        public void OnDisable()
        {
            var damageableComponent = GetComponent<IDamageableComponent>();
            damageableComponent?.UnregisterBar(this);
        }

        /// <summary>
        ///     Auto assign the bar component to the bar variable
        ///     in the inspector
        /// </summary>
        public void OnValidate()
        {
            if (bar != null)
            {
                return;
            }

            var barsOnObject = GetComponentsInChildren<Bar>();
            foreach (var barOnObject in barsOnObject)
            {
                if (barOnObject.name.Contains("Health Bar"))
                {
                    bar = barOnObject;
                    break;
                }
            }
        }

        public float TakeDamage(float amount)
        {
            var appliedDamage = Mathf.Min(amount, CurrentValue);
            CurrentValue -= appliedDamage;
            amount -= appliedDamage;

            if (amount > 0)
            {
                amount = AfterDamageOverFlow?.Invoke(amount) ?? amount;
            }

            return amount;
        }

        public float TakeHeal(float amount)
        {
            var appliedHeal = Mathf.Min(amount, MaxValue - CurrentValue);
            CurrentValue += appliedHeal;
            amount -= appliedHeal;

            if (amount > 0)
            {
                amount = AfterHealOverFlow?.Invoke(amount) ?? amount;
            }

            return amount;
        }

        public DamageableComponentType ModulePriority => DamageableComponentType.HealthBar;
        public AfterOverFlowCallback AfterDamageOverFlow { get; set; }
        public AfterOverFlowCallback AfterHealOverFlow { get; set; }

        public bool IsDestroyed => CurrentValue <= 0;

        public void Heal(float healAmount) => CurrentValue += healAmount;

        public void ActivateHealing(float amount, float duration) => ActivateProgressChange(amount, duration);

        public void SetRegenRate(float healthRegenRate) => regenRate = healthRegenRate;
    }
}
