using System;
using System.Collections.Generic;
using UnitParts;
using UnityEngine;
using UnityEngine.Serialization;

namespace EntityData
{
    [Serializable]
    public class ResourceCost
    {
        public ResourceKind resourceType;
        public int amount;
    }


    [Serializable]
    [CreateAssetMenu(fileName = "Building Data", menuName = "SO/Entity/Building")]
    public class BuildingData : ScriptableObject
    {
        [Tooltip("How many building resources are required to build this building")] [SerializeField]
        public List<ResourceCost> costsList;


        [Tooltip("Name of the building that is going to show in dialogs and HUD")] [SerializeField]
        public string buildingName;

        [Space(10)] [Tooltip("The are of a building in tiles of the grid")] [SerializeField]
        public Vector2Int dimensions = Vector2Int.one;


        [Tooltip("Whether it is possible to rotate a building")] [SerializeField]
        public bool isRotateable;

        [Tooltip("Whether placing this building should recompute NavMesh")] [SerializeField]
        public bool isDisruptingNavMesh;

        [Space(10)] [Tooltip("Maximum number of health points")] [SerializeField] [Min(0)]
        public int maxHealth;

        [Tooltip("How fast the building regenerates health")] [SerializeField] [Min(0)]
        public float healthRegenRate;

        [FormerlySerializedAs("priority")] [Tooltip("How viable target it is for enemies")] [SerializeField]
        public int targetPriority;

        [Tooltip("To which team does the building belong")] [SerializeField]
        public TeamEnum team;

        public Dictionary<ResourceKind, int> Costs
        {
            get
            {
                var dict = new Dictionary<ResourceKind, int>();
                foreach (var cost in costsList)
                {
                    dict[cost.resourceType] = cost.amount;
                }

                return dict;
            }
        }
    }
}
