namespace NavMeshCustom
{
    /// <summary>
    ///     Interface for NavMeshManager to enable dependency injection
    /// </summary>
    public interface INavMeshManager
    {
        /// <summary>
        ///     Check if a rebuild is currently needed
        /// </summary>
        bool needsRebuild { get; }

        /// <summary>
        ///     Request a rebuild of the navigation mesh
        /// </summary>
        void RequestRebuildNavmesh();
    }
}
