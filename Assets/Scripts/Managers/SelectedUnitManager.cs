using Common;
using NaughtyAttributes;
using System.Collections;
using UnitParts;
using UnitParts.Controllers;
using UnitParts.GroupManagement;
using UnityEngine;

namespace Managers
{
    [DefaultExecutionOrder(-50)]
    public class SelectedUnitManager : MonoBehaviour, ISelectedUnitManager
    {
        [SerializeField] [Required]
        public Transform selectionBox; // Reference to the UI RectTransform for the selection box

        [SerializeField] public LayerMask unitLayer; // Layer for units

        [SerializeField] public float minSelectionBoxSize = 50f; // Minimum size for the selection box

        [SerializeField] private GameObject orderMarker;

        [SerializeField] private float moveOrderMarkerLifetime = 1;

        private readonly AssignedUnitsGroup _assignedUnitsGroup = new();
        private Camera _camera;
        private bool _clickMode; // Ignore selection box if the distance is too small
        private IOrderPoolManager _orderPoolManager;

        private Vector2 _startMousePosition, _endMousePosition;
        private Vector2 boxStart, boxEnd;

        private void Awake()
        {
            // Register this instance in the service locator
            ServiceLocator.Register<ISelectedUnitManager>(this);

            // Ensure this manager persists across scenes
            DontDestroyOnLoad(gameObject);
        }

        private void Reset()
        {
            selectionBox.gameObject.SetActive(false);
            ClearSelectedUnits();
        }

        private void Start()
        {
            _orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();

            _assignedUnitsGroup.OnAssigment += unit =>
            {
                unit.selection.Toggle(true);
            };
            _assignedUnitsGroup.OnUnassignment += unit =>
            {
                unit.selection.Toggle(false);
            };
            _camera = Camera.main;
        }

        private void Update()
        {
            if (_camera is null)
            {
                return;
            }

            boxStart = _camera.ScreenToWorldPoint(_startMousePosition);
            boxEnd = _camera.ScreenToWorldPoint(_endMousePosition);

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                Reset();
            }

            // Start drawing selection box
            if (Input.GetMouseButtonDown(0))
            {
                ClearSelectedUnits();
                _startMousePosition = Input.mousePosition;
                // Reset selection box mode
                _clickMode = true;
            }

            // Update selection box position and size
            if (Input.GetMouseButton(0))
            {
                _endMousePosition = Input.mousePosition;
                if (_clickMode)
                {
                    if (Vector2.Distance(_startMousePosition, _endMousePosition) > minSelectionBoxSize)
                    {
                        _clickMode = false;
                        selectionBox.gameObject.SetActive(true);
                        UpdateSelectionBox();
                    }
                }
                else
                {
                    UpdateSelectionBox();
                }
            }

            // right click release
            if (Input.GetMouseButtonUp(1))
            {
                IssueCommandUnits();
            }

            // left click release
            if (Input.GetMouseButtonUp(0))
            {
                if (!_clickMode)
                {
                    // Selection mode
                    selectionBox.gameObject.SetActive(false);
                    SelectUnits();
                }
            }
        }

        private void OnDisable() => Reset();

        private void OnDestroy() =>
            // Unregister from service locator when destroyed
            ServiceLocator.Unregister<ISelectedUnitManager>();

        public void OnDrawGizmos()
        {
            foreach (var unit in _assignedUnitsGroup.Units)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(unit.transform.position, 0.5f);
            }
        }

        public void ClearSelectedUnits()
        {
            Debug.Log("Clearing selected units " + _assignedUnitsGroup.Units.Count);
            _assignedUnitsGroup.UnassignAll();
        }

        public int GetSelectedCount() => _assignedUnitsGroup.Units.Count;

        private void IssueCommandUnits()
        {
            // Click mode
            var positionInWorld = _camera.ScreenToWorldPoint(Input.mousePosition);
            positionInWorld.z = 0;

            if (_assignedUnitsGroup.Units.Count > 0)
            {
                StartCoroutine(ShowOrderMarker(positionInWorld));
            }

            foreach (var unit in _assignedUnitsGroup.Units)
            {
                if (Input.GetKey(KeyCode.LeftShift))
                {
                    unit.unitCommander.CancelOrdersNotFromIssuer(this);
                }
                else
                {
                    unit.unitCommander.CancelAllOrders();
                }

                if (_orderPoolManager != null)
                {
                    if (Input.GetKey(KeyCode.LeftControl))
                    {
                        var order = OrderUtils.MoveToStaticTarget(unit, positionInWorld);
                        unit.unitCommander.IssueOrder(order);
                    }
                    else
                    {
                        var order = OrderUtils.AttackMove(unit, positionInWorld);
                        unit.unitCommander.IssueOrder(order);
                    }
                }
            }

            if (!Input.GetKey(KeyCode.LeftShift))
            {
                ClearSelectedUnits();
            }
        }

        private void UpdateSelectionBox()
        {
            var boxCenter = (boxStart + boxEnd) / 2;
            var boxSize = new Vector2(Mathf.Abs(boxStart.x - boxEnd.x), Mathf.Abs(boxStart.y - boxEnd.y));

            selectionBox.position = new Vector3(boxCenter.x, boxCenter.y, 0);
            selectionBox.transform.localScale = boxSize;
        }

        private void SelectUnits()
        {
            var unitColliders = Physics2D.OverlapAreaAll(boxStart, boxEnd, unitLayer);
            Debug.Log("Selected " + unitColliders.Length + " units" + unitColliders);

            foreach (var unitCollider in unitColliders)
            {
                var unit = unitCollider.GetComponent<UnitController>();

                if (!unit)
                {
                    continue;
                }

                if (unit.Team != TeamEnum.Player)
                {
                    continue;
                }

                _assignedUnitsGroup.AssignUnit(unit);
            }
        }

        private IEnumerator ShowOrderMarker(Vector3 targetPosition)
        {
            // we do not care about the Z position of the target.
            targetPosition.z = 0.0f;
            var marker = Instantiate(orderMarker, targetPosition, Quaternion.identity);
            yield return new WaitForSeconds(moveOrderMarkerLifetime);
            Destroy(marker.gameObject);
        }
    }
}
