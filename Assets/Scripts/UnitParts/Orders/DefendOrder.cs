using Common;
using Managers;
using System;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace UnitParts.Orders
{
    [Serializable]
    public class DefendOrder : BaseOrder
    {
        public Vector3 DefendPosition;
        public float DefendRadius;
        public float DetectionRadius;
        public UnitController UnitController;

        private IBaseOrder _currentSubOrder;
        private bool _isDefending = true;

        public DefendOrder Initialize(MonoBehaviour issuer, Vector3 defendPosition, float defendRadius,
            float detectionRadius, UnitController unitController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            DefendPosition = defendPosition;
            DefendRadius = defendRadius;
            DetectionRadius = detectionRadius;
            UnitController = unitController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.DefendOrderPool?.Return(this);
        }

        public static new DefendOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.DefendOrderPool?.Get() ?? new DefendOrder();
        }

        public override void Execute() => StartDefendBehavior();

        private void StartDefendBehavior()
        {
            // Create a stay still order at the defend position
            var stayOrder = CreateStayAtPositionOrder();

            // Wrap it with enemy detection
            var detectionDecorator = new EnemyDetectionDecorator(stayOrder)
            {
                detectionRadius = DetectionRadius,
                onEnemyDetected = OnEnemyDetected,
                onNoEnemiesDetected = OnNoEnemiesDetected
            };

            _currentSubOrder = detectionDecorator;
            _currentSubOrder.Execute();
        }

        private IBaseOrder CreateStayAtPositionOrder()
        {
            // If not at defend position, move there first
            var distanceToDefendPos = Vector3.Distance(UnitController.Position, DefendPosition);
            if (distanceToDefendPos > 0.5f)
            {
                return OrderUtils.MoveToStaticTarget(UnitController, DefendPosition);
            }

            return CreateStayStillOrder();
        }

        private IBaseOrder CreateStayStillOrder()
        {
            var stayStillOrder = StayStillOrder.Get();
            stayStillOrder.Initialize(UnitController, UnitController.movementController);
            return stayStillOrder;
        }

        private void OnEnemyDetected(ITeamDamageable enemy)
        {
            if (!_isDefending)
            {
                return;
            }

            _isDefending = false;
            _currentSubOrder?.Cancel();

            // Create attack order with radius constraint
            var attackOrder = CreateConstrainedAttackOrder(enemy);
            _currentSubOrder = attackOrder;
            _currentSubOrder.Execute();
        }

        private void OnNoEnemiesDetected()
        {
            if (_isDefending)
            {
                return;
            }

            _isDefending = true;
            _currentSubOrder?.Cancel();

            // Return to defend position
            StartDefendBehavior();
        }

        private IBaseOrder CreateConstrainedAttackOrder(ITeamDamageable target)
        {
            var attackOrder = OrderUtils.DirectAttackOrder(UnitController, target);

            // Add radius constraint using conditional decorator
            var radiusConstraintDecorator = new ConditionalDecorator(attackOrder)
            {
                condition = () =>
                {
                    // Cancel attack if target is too far from defend position
                    var distanceFromDefendPos = Vector3.Distance(target.Position, DefendPosition);
                    return distanceFromDefendPos > DefendRadius || target.IsDestroyed;
                },
                onCondition = () => OnNoEnemiesDetected()
            };

            return radiusConstraintDecorator;
        }

        public override void Cancel()
        {
            _currentSubOrder?.Cancel();
            InvokeOnCancel();
        }
    }
}
