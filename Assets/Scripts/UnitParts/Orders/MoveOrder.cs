using Common;
using Managers;
using System;
using UnitParts.Interfaces;
using UnityEngine;

namespace UnitParts.Orders
{
    [Serializable]
    public class MoveOrder : BaseOrder
    {
        public Vector3 TargetPosition;
        public float StoppingDistance;
        public IMovementController MovementController;

        public MoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition,
            IMovementController movementController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            StoppingDistance = -1;
            MovementController = movementController;

            return this;
        }

        public MoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition, float stoppingDistance,
            IMovementController movementController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            StoppingDistance = stoppingDistance;
            MovementController = movementController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.MoverOrderPool.Return(this);
        }

        public static new MoveOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.MoverOrderPool.Get();
        }

        public override void Execute()
        {
            Debug.Log($"[MoveOrder] Moving to {TargetPosition} with stopping distance {StoppingDistance}");
            if (StoppingDistance < 0)
            {
                MovementController.MoveTo(TargetPosition, InvokeOnComplete);
            }
            else
            {
                MovementController.MoveTo(TargetPosition, StoppingDistance, InvokeOnComplete);
            }
        }

        public override void Cancel()
        {
            MovementController.StopMovement();
            InvokeOnCancel();
        }
    }
}
