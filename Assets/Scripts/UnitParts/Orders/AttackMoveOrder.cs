using Common;
using Managers;
using System;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace UnitParts.Orders
{
    [Serializable]
    public class AttackMoveOrder : BaseOrder
    {
        public Vector3 TargetPosition;
        public float DetectionRadius;
        public UnitController UnitController;

        private IBaseOrder _currentSubOrder;
        private bool _isMoving = true;
        private Vector3 _lastPosition;

        public AttackMoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition, float detectionRadius,
            UnitController unitController, Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            DetectionRadius = detectionRadius;
            UnitController = unitController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.AttackMoveOrderPool?.Return(this);
        }

        public static new AttackMoveOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.AttackMoveOrderPool?.Get() ?? new AttackMoveOrder();
        }

        public override void Execute()
        {
            _lastPosition = UnitController.Position;
            StartMovementWithDetection();
        }

        private void StartMovementWithDetection()
        {
            // Create move order to target position (static position)
            var moveOrder = OrderUtils.MoveToStaticTarget(UnitController, TargetPosition);
            moveOrder.OnComplete += () => InvokeOnComplete(); // Complete when destination reached

            // Wrap with enemy detection
            var detectionDecorator = new EnemyDetectionDecorator(moveOrder)
            {
                detectionRadius = DetectionRadius,
                onEnemyDetected = OnEnemyDetected,
                onNoEnemiesDetected = OnNoEnemiesDetected
            };

            _currentSubOrder = detectionDecorator;
            _currentSubOrder.Execute();
            _isMoving = true;
        }

        private void OnEnemyDetected(ITeamDamageable enemy)
        {
            if (!_isMoving)
            {
                return;
            }

            var enemyName = (enemy as MonoBehaviour)?.name ?? "Unknown Enemy";
            Debug.Log($"[AttackMove] Enemy detected: {enemyName}, switching to attack mode");
            _isMoving = false;
            _lastPosition = UnitController.Position; // Remember where we stopped
            _currentSubOrder?.Cancel();
            _currentSubOrder = null;

            // Create attack order
            var attackOrder = CreateAttackOrder(enemy);
            attackOrder.OnComplete += () =>
            {
                attackOrder.Execute();
            }
            _currentSubOrder = attackOrder;
            _currentSubOrder.Execute();
        }

        private void OnNoEnemiesDetected()
        {
            if (_isMoving)
            {
                return;
            }

            Debug.Log("[AttackMove] No enemies detected, resuming movement");
            // Resume movement from current position to target
            ResumeMovement();
        }

        private void ResumeMovement()
        {
            if (_isMoving)
            {
                Debug.Log("[AttackMove] Already moving, ignoring resume request");
                return;
            }

            Debug.Log("[AttackMove] Resuming movement to target");
            _isMoving = true;
            _currentSubOrder?.Cancel();
            _currentSubOrder = null;

            // Check if we've reached the target
            var distanceToTarget = Vector3.Distance(UnitController.Position, TargetPosition);
            if (distanceToTarget <= 1f) // Close enough to target
            {
                Debug.Log("[AttackMove] Reached target, completing order");
                InvokeOnComplete();
                return;
            }

            // Resume movement from current position to target
            StartMovementWithDetection();
        }

        private IBaseOrder CreateAttackOrder(ITeamDamageable target)
        {
            var attackOrder = OrderUtils.DirectAttackOrder(UnitController, target);

            // Add condition to stop attacking when target is dead or out of detection range
            var attackConditionDecorator = new ConditionalDecorator(attackOrder)
            {
                condition = () =>
                {
                    try
                    {
                        if (target == null || target.IsDestroyed)
                        {
                            return true;
                        }

                        var distanceToTarget = Vector3.Distance(UnitController.Position, target.Position);
                        return distanceToTarget > DetectionRadius * 1.1f; // Small buffer to prevent oscillation
                    }
                    catch (NullReferenceException)
                    {
                        Debug.Log("[AttackMove] Target became invalid or out of range, stopping attack");
                        return true;
                    }
                },
                onCondition = () => ResumeMovement()
            };

            return attackConditionDecorator;
        }

        /// <summary>
        ///     Calculate optimal attack position that maintains proper distance from target for effective gun targeting
        /// </summary>
        private Vector3 CalculateOptimalAttackPosition(ITeamDamageable target)
        {
            if (!UnitController.gunController?.gun)
            {
                // No gun, move directly to target
                return target.Position;
            }

            var gunRange = UnitController.gunController.gun.range;
            var currentDistance = Vector3.Distance(UnitController.Position, target.Position);

            // Define optimal distance range
            var minDistance = gunRange * 0.2f; // Minimum distance to avoid getting too close
            var optimalDistance = gunRange * 0.8f; // Optimal distance for targeting

            // If already at good distance, stay put
            if (currentDistance >= minDistance && currentDistance <= optimalDistance)
            {
                return UnitController.Position;
            }

            // Calculate direction from target to unit
            var directionToUnit = (UnitController.Position - target.Position).normalized;

            // If unit is too close or direction is invalid, use a default direction
            if (directionToUnit.magnitude < 0.1f)
            {
                // Try different directions to find a good position
                var angles = new[] { 0f, 90f, 180f, 270f, 45f, 135f, 225f, 315f };
                foreach (var angle in angles)
                {
                    var direction = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), Mathf.Sin(angle * Mathf.Deg2Rad), 0);
                    var testPosition = target.Position + (direction * optimalDistance);

                    // Use the first direction that doesn't overlap with the target
                    if (Vector3.Distance(testPosition, target.Position) >= minDistance)
                    {
                        directionToUnit = direction;
                        break;
                    }
                }

                // Fallback if no good direction found
                if (directionToUnit.magnitude < 0.1f)
                {
                    directionToUnit = Vector3.right;
                }
            }

            // Calculate optimal position
            var optimalPosition = target.Position + (directionToUnit * optimalDistance);

            return optimalPosition;
        }

        public override void Cancel()
        {
            _currentSubOrder?.Cancel();
            InvokeOnCancel();
        }
    }
}
