using UnityEngine;

namespace UnitParts.Interfaces
{
    /// This interface is used to identify entities that have a team
    public interface ITeamEntity : IEntity
    {
        /// <summary>
        /// Team of the entity
        /// </summary>
        TeamEnum Team { get; }
    }

    /// This interface is used to identify entities that can take damage
    public interface ITeamDamageable : ITeamEntity, IDamageable
    {
        /// <summary>
        ///     How viable target it is for enemies
        /// </summary>
        public int TargetPriority { get; }

        OnDestroyCallback OnDestroyCallback { get; set; }
        void Knockback(Vector3 knockbackDirection, float knockback);
    }

    /// <summary>
    ///     Called once the entity is destroyed
    /// </summary>
    public delegate void OnDestroyCallback();
}
