using Common;
using Managers;
using NaughtyAttributes;
using NavMeshPlus.Components;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnitParts;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace NavMeshCustom
{
    public class NavMeshManager : MonoBeh<PERSON>our, INavMeshManager
    {
        [ReadOnly] [SerializeField] private NavMeshSurface[] navMeshSurfaces;

        [Tooltip("Prevents multiple rebuild requests from being sent in a short time.")]
        [BoxGroup("Batch Rebuild")]
        [SerializeField]
        private bool batchRebuild = true;

        [ShowIf("batchRebuild")]
        [Tooltip("Time interval to batch rebuild NavMesh surfaces.")]
        [BoxGroup("Batch Rebuild")]
        [SerializeField]
        private float batchRebuildInterval = 1f;

        [BoxGroup("Batch Rebuild")] [ShowIf("batchRebuild")] [SerializeField] [ReadOnly]
        private bool _needsRebuild = true;

        [Tooltip("Prevents agents from stopping when the NavMesh is rebuilt.")]
        [BoxGroup("Parallel Rebuild")]
        [SerializeField]
        private bool parallelRebuild = true;

        [Tooltip("Maximum time per frame (in milliseconds) for NavMesh operations to prevent hitches.")]
        [BoxGroup("Performance")]
        [SerializeField]
        private float frameBudgetMs = 2f;

        [Tooltip("Delay before processing each NavMesh surfaces.")] [BoxGroup("Performance")] [SerializeField]
        private float surfaceProcessingDelay = 0.1f;

        [SerializeField] [ReadOnly] private float lastRebuildTime;
        [SerializeField] [ReadOnly] private bool _isRebuilding = false;

        // NavMeshSurface pooling system
        private readonly Queue<GameObject> _navMeshSurfacePool = new();
        private readonly List<NavMeshSurfaceRebuildOperation> _activeOperations = new();

        private void Awake()
        {
            // Register this instance in the service locator
            ServiceLocator.Register<INavMeshManager>(this);

            // NavMeshManager should not persist across scenes (dontDestroy = false)
            navMeshSurfaces = GetComponentsInChildren<NavMeshSurface>();
        }

        private void Start()
        {
            lastRebuildTime = Time.time - batchRebuildInterval;
            RequestRebuildNavmesh();
        }

        private void Update()
        {
            if ((_needsRebuild && Time.time - lastRebuildTime > batchRebuildInterval && !_isRebuilding) ||
                (!batchRebuild && !_isRebuilding))
            {
                StartCoroutine(RebuildNavmeshCoroutine());
            }
        }

        private void OnDisable()
        {
            // Clean up any active operations
            _activeOperations.Clear();

            // Clean up pool
            while (_navMeshSurfacePool.Count > 0)
            {
                var pooledObject = _navMeshSurfacePool.Dequeue();
                if (pooledObject != null)
                {
                    Destroy(pooledObject);
                }
            }

            // Unregister from service locator when destroyed
            ServiceLocator.Unregister<INavMeshManager>();
        }

        public bool needsRebuild => _needsRebuild;

        public void RequestRebuildNavmesh()
        {
            if (navMeshSurfaces.Length == 0)
            {
                Debug.LogWarning("No NavMeshSurfaces found.");
                return;
            }

            _needsRebuild = true;
        }

        /// <summary>
        /// Coroutine-based NavMesh rebuilding that spreads work across multiple frames
        /// </summary>
        private IEnumerator RebuildNavmeshCoroutine()
        {
            _isRebuilding = true;
            _needsRebuild = false;
            lastRebuildTime = Time.time;

            Debug.Log($"Starting optimized NavMesh rebuild for {navMeshSurfaces.Length} surfaces.");

            // Small delay before starting rebuild
            // Gives time be able to place multiple buildings before starting the rebuild
            yield return new WaitForSeconds(surfaceProcessingDelay);

            if (!parallelRebuild)
            {
                yield return StartCoroutine(RebuildNavmeshSequential());
            }
            else
            {
                yield return StartCoroutine(RebuildNavmeshParallel());
            }

            Debug.Log("NavMesh rebuild completed.");
            _isRebuilding = false;
        }

        /// <summary>
        /// Sequential NavMesh rebuilding - rebuilds surfaces one by one with frame budget control
        /// </summary>
        private IEnumerator RebuildNavmeshSequential()
        {
            var frameTimer = Stopwatch.StartNew();

            foreach (var navMeshSurface in navMeshSurfaces)
            {
                if (!navMeshSurface.gameObject.activeSelf)
                {
                    continue;
                }

                navMeshSurface.BuildNavMeshAsync();

                // Check frame budget before processing each surface
                if (frameTimer.ElapsedMilliseconds > frameBudgetMs)
                {
                    yield return null; // Wait for next frame
                    frameTimer.Restart();
                }
            }
        }

        /// <summary>
        /// Parallel NavMesh rebuilding using pooled NavMeshSurface objects
        /// </summary>
        private IEnumerator RebuildNavmeshParallel()
        {
            var frameTimer = Stopwatch.StartNew();
            var newSurfaces = new List<NavMeshSurface>();

            foreach (var navMeshSurface in navMeshSurfaces)
            {
                if (!navMeshSurface.gameObject.activeSelf)
                {
                    continue;
                }

                var navMeshName = navMeshSurface.name;
                navMeshSurface.gameObject.name = $"{navMeshName} (old)";

                // Get or create new NavMeshSurface from pool
                var newNavMeshSurface = GetPooledNavMeshSurface(navMeshSurface);
                if (newNavMeshSurface == null)
                {
                    Debug.LogError($"Failed to get pooled NavMeshSurface for {navMeshName}");
                    continue;
                }

                newNavMeshSurface.gameObject.name = $"{navMeshName} (new)";
                newSurfaces.Add(newNavMeshSurface);

                // Create rebuild operation to track completion
                var operation = new NavMeshSurfaceRebuildOperation
                {
                    OldSurface = navMeshSurface, NewSurface = newNavMeshSurface, OriginalName = navMeshName
                };

                var asyncOp = newNavMeshSurface.BuildNavMeshAsync();
                operation.AsyncOperation = asyncOp;
                _activeOperations.Add(operation);

                asyncOp.completed += _ => OnNavMeshRebuildCompleted(operation);

                // Check frame budget before processing each surface
                if (frameTimer.ElapsedMilliseconds > frameBudgetMs)
                {
                    yield return null; // Wait for next frame
                    frameTimer.Restart();
                }
            }

            // Wait for all operations to complete
            while (_activeOperations.Count > 0)
            {
                yield return null;
            }

            navMeshSurfaces = newSurfaces.ToArray();
        }

        /// <summary>
        ///     Gets the NavMeshAgentType based on the team, with optional inversion
        /// </summary>
        /// <param name="team">The team to get the agent type for</param>
        /// <param name="invert">If true, returns the opposite team's agent type</param>
        /// <returns>The corresponding NavMeshAgentType</returns>
        public static NavMeshAgentType GetAgentTypeBasedOnTeam(TeamEnum team, bool invert = false)
        {
            if (invert)
            {
                switch (team)
                {
                    case TeamEnum.Player:
                        return NavMeshAgentType.Enemy;
                    case TeamEnum.Enemy:
                        return NavMeshAgentType.Player;
                    default:
                        throw new Exception("Team " + team + " not found");
                }
            }

            switch (team)
            {
                case TeamEnum.Player:
                    return NavMeshAgentType.Player;
                case TeamEnum.Enemy:
                    return NavMeshAgentType.Enemy;
                default:
                    throw new Exception("Team " + team + " not found");
            }
        }

        /// <summary>
        /// Gets a pooled NavMeshSurface or creates a new one if pool is empty
        /// </summary>
        private NavMeshSurface GetPooledNavMeshSurface(NavMeshSurface template)
        {
            GameObject pooledObject;

            if (_navMeshSurfacePool.Count > 0)
            {
                pooledObject = _navMeshSurfacePool.Dequeue();

                // Ensure the pooled object is still valid
                if (pooledObject == null)
                {
                    // If pooled object was destroyed, create a new one
                    pooledObject = Instantiate(template.gameObject, template.transform.parent);
                }

                pooledObject.SetActive(false);
            }
            else
            {
                pooledObject = Instantiate(template.gameObject, template.transform.parent);
                pooledObject.SetActive(false);
            }

            var navMeshSurface = pooledObject.GetComponent<NavMeshSurface>();
            if (navMeshSurface == null)
            {
                Debug.LogError("Pooled object does not have NavMeshSurface component!");
                return null;
            }

            return navMeshSurface;
        }

        /// <summary>
        /// Returns a NavMeshSurface GameObject to the pool for reuse
        /// </summary>
        private void ReturnToPool(GameObject navMeshSurfaceObject)
        {
            if (navMeshSurfaceObject != null)
            {
                navMeshSurfaceObject.SetActive(false);
                navMeshSurfaceObject.name = "Pooled NavMeshSurface";
                _navMeshSurfacePool.Enqueue(navMeshSurfaceObject);
            }
        }

        /// <summary>
        /// Called when a NavMesh rebuild operation completes
        /// </summary>
        private void OnNavMeshRebuildCompleted(NavMeshSurfaceRebuildOperation operation)
        {
            if (operation.NewSurface != null)
            {
                operation.NewSurface.gameObject.name = operation.OriginalName;
                operation.NewSurface.gameObject.SetActive(true);
            }

            if (operation.OldSurface != null)
            {
                ReturnToPool(operation.OldSurface.gameObject);
            }

            _activeOperations.Remove(operation);
        }
    }

    /// <summary>
    /// Helper class to track NavMesh rebuild operations
    /// </summary>
    public class NavMeshSurfaceRebuildOperation
    {
        public NavMeshSurface OldSurface;
        public NavMeshSurface NewSurface;
        public string OriginalName;
        public AsyncOperation AsyncOperation;
    }
}
